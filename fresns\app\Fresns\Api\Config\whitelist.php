<?php

/*
 * Fr<PERSON><PERSON> (https://fresns.org)
 * Copyright (C) 2021-Present <PERSON><PERSON>
 * Released under the Apache-2.0 License.
 */

return [
    /*
    |-------------------------------------
    | No login required to use
    |-------------------------------------
    */

    // Public mode account
    'publicAccount' => [
        'api.global.status',
        'api.global.configs',
        'api.global.language.pack',
        'api.global.channels',
        'api.global.content.types',
        'api.global.roles',
        'api.global.stickers',
        'api.common.ip.info',
        'api.common.input.tips',
        'api.common.cmd.word',
        'api.common.file.users',
        'api.search.users',
        'api.search.groups',
        'api.search.hashtags',
        'api.search.geotags',
        'api.search.posts',
        'api.search.comments',
        'api.account.login',
        'api.user.list',
        'api.user.detail',
        'api.user.followers.you.follow',
        'api.user.interaction',
        'api.user.mark.list',
        'api.group.tree',
        'api.group.list',
        'api.group.detail',
        'api.group.creator',
        'api.group.admins',
        'api.group.interaction',
        'api.hashtag.list',
        'api.hashtag.detail',
        'api.hashtag.interaction',
        'api.geotag.list',
        'api.geotag.detail',
        'api.geotag.interaction',
        'api.post.list',
        'api.post.nearby',
        'api.post.detail',
        'api.post.interaction',
        'api.post.users',
        'api.post.quotes',
        'api.post.histories',
        'api.post.history.detail',
        'api.comment.list',
        'api.comment.nearby',
        'api.comment.detail',
        'api.comment.interaction',
        'api.comment.histories',
        'api.comment.history.detail',
    ],

    // Public mode user
    'publicUser' => [
        'api.global.status',
        'api.global.configs',
        'api.global.language.pack',
        'api.global.channels',
        'api.global.content.types',
        'api.global.roles',
        'api.global.stickers',
        'api.common.ip.info',
        'api.common.input.tips',
        'api.common.cmd.word',
        'api.common.file.users',
        'api.search.users',
        'api.search.groups',
        'api.search.hashtags',
        'api.search.geotags',
        'api.search.posts',
        'api.search.comments',
        'api.account.login',
        'api.account.logout',
        'api.account.detail',
        'api.account.wallet.records',
        'api.user.login',
        'api.user.overview',
        'api.user.list',
        'api.user.detail',
        'api.user.followers.you.follow',
        'api.user.interaction',
        'api.user.mark.list',
        'api.group.tree',
        'api.group.list',
        'api.group.detail',
        'api.group.creator',
        'api.group.admins',
        'api.group.interaction',
        'api.hashtag.list',
        'api.hashtag.detail',
        'api.hashtag.interaction',
        'api.geotag.list',
        'api.geotag.detail',
        'api.geotag.interaction',
        'api.post.list',
        'api.post.nearby',
        'api.post.detail',
        'api.post.interaction',
        'api.post.users',
        'api.post.quotes',
        'api.post.histories',
        'api.post.history.detail',
        'api.comment.list',
        'api.comment.nearby',
        'api.comment.detail',
        'api.comment.interaction',
        'api.comment.histories',
        'api.comment.history.detail',
    ],

    // Private mode account
    'privateAccount' => [
        'api.global.status',
        'api.global.configs',
        'api.global.language.pack',
        'api.global.channels',
        'api.common.ip.info',
        'api.common.cmd.word',
        'api.account.login',
    ],

    // Private mode user
    'privateUser' => [
        'api.global.status',
        'api.global.configs',
        'api.global.language.pack',
        'api.global.channels',
        'api.common.ip.info',
        'api.common.cmd.word',
        'api.account.login',
        'api.account.logout',
        'api.account.detail',
        'api.account.wallet.records',
        'api.user.login',
        'api.user.overview',
    ],
];
