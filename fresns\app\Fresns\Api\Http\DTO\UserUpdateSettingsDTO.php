<?php

/*
 * Fresns (https://fresns.org)
 * Copyright (C) 2021-Present <PERSON><PERSON>
 * Released under the Apache-2.0 License.
 */

namespace App\Fresns\Api\Http\DTO;

use Fresns\DTO\DTO;

class UserUpdateSettingsDTO extends DTO
{
    public function rules(): array
    {
        return [
            'conversationPolicy' => ['integer', 'nullable', 'in:1,2,3,4'],
            'commentPolicy' => ['integer', 'nullable', 'in:1,2,3,4'],
            'deviceToken' => ['string', 'nullable'],
        ];
    }
}
